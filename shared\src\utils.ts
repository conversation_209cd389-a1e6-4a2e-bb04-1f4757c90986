import { RpaStep, ConditionalClickStep, DownloadFileStep, ExtractPdfValuesStep, FortnoxCreateVoucherStep } from './types/steps';
import { FlowVariableRegistry } from './types';

// Base types for validation and API responses
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Utility functions
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function createEmptyFlow(): any {
  return {
    id: generateId(),
    name: '',
    description: '',
    customerId: '',
    steps: [],
    variables: {},
    variableRegistry: {
      static: {},
      dynamic: {}
    },
    settings: {
      viewport: { width: 1920, height: 1080 },
      timeout: 30000
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }

  const seconds = Math.floor(ms / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
}

// Step creation utilities
export function createStepFromType(stepType: string): RpaStep {
  const baseStep = {
    id: generateId(),
    name: '',
    description: '',
  };

  switch (stepType) {
    case 'navigate':
      return { ...baseStep, type: 'navigate', name: 'Navigera', url: '' }
    case 'goBack':
      return { ...baseStep, type: 'goBack', name: 'Gå tillbaka' }
    case 'goForward':
      return { ...baseStep, type: 'goForward', name: 'Gå framåt' }
    case 'reload':
      return { ...baseStep, type: 'reload', name: 'Ladda om' }
    case 'click':
      return { ...baseStep, type: 'click', name: 'Klicka', selector: '' }
    case 'fill':
      return { ...baseStep, type: 'fill', name: 'Fyll i', selector: '', value: '' }
    case 'type':
      return { ...baseStep, type: 'type', name: 'Skriv', selector: '', text: '' }
    case 'selectOption':
      return { ...baseStep, type: 'selectOption', name: 'Välj alternativ', selector: '' }
    case 'check':
      return { ...baseStep, type: 'check', name: 'Markera', selector: '' }
    case 'uncheck':
      return { ...baseStep, type: 'uncheck', name: 'Avmarkera', selector: '' }
    case 'waitForSelector':
      return { ...baseStep, type: 'waitForSelector', name: 'Vänta på element', selector: '', timeout: 5000 }
    case 'waitForTimeout':
      return { ...baseStep, type: 'waitForTimeout', name: 'Vänta tid', duration: 1000 }
    case 'waitForUrl':
      return { ...baseStep, type: 'waitForUrl', name: 'Vänta på URL', url: '' }
    case 'extractText':
      return { ...baseStep, type: 'extractText', name: 'Extrahera text', selector: '', variableName: '' }
    case 'extractAttribute':
      return { ...baseStep, type: 'extractAttribute', name: 'Extrahera attribut', selector: '', attribute: 'href', variableName: '' }
    case 'takeScreenshot':
      return { ...baseStep, type: 'takeScreenshot', name: 'Ta skärmbild', path: 'screenshot.png' }
    case 'fillPassword':
      return { ...baseStep, type: 'fillPassword', name: 'Fyll i lösenord', selector: '', credentialId: '' }
    case 'fill2FA':
      return { ...baseStep, type: 'fill2FA', name: 'Fyll i 2FA', selector: '', credentialId: '' }
    case 'ifElementExists':
      return { ...baseStep, type: 'ifElementExists', name: 'Om element finns', selector: '', thenSteps: [], elseSteps: [] }
    case 'conditionalClick':
      return { ...baseStep, type: 'conditionalClick', name: 'Villkorlig klick', selector: '', condition: 'exists' }
    case 'downloadFile':
      return { ...baseStep, type: 'downloadFile', name: 'Ladda ner fil', triggerSelector: '', filename: '', saveToFile: false }
    case 'extractPdfValues':
      return { ...baseStep, type: 'extractPdfValues', name: 'Extrahera PDF-värden', base64Input: '', prompt: 'Extrahera namn, telefonnummer och email från dokumentet' }
    case 'fortnoxCreateVoucher':
      return {
        ...baseStep,
        type: 'fortnoxCreateVoucher',
        name: 'Skapa Fortnox Verifikation',
        voucherSeries: 'A',
        accountMappings: [
          {
            accountNumber: '',
            variableName: '',
            debitCredit: 'debit'
          },
          {
            accountNumber: '',
            variableName: '',
            debitCredit: 'credit'
          }
        ]
      }
    default:
      throw new Error(`Unknown step type: ${stepType}`)
  }
}

/**
 * Variable registry utilities for RPA flows
 */

/**
 * Analyzes a step and returns the variables it creates
 */
export function getStepVariables(step: RpaStep): {
  static: { [variableName: string]: { stepType: string; description: string } };
  dynamic?: { stepType: string; description: string; commonPatterns?: string[] };
} {
  const result: any = { static: {} };

  switch (step.type) {
    case 'extractText':
      const extractTextStep = step as any;
      if (extractTextStep.variableName) {
        result.static[extractTextStep.variableName] = {
          stepType: step.type,
          description: `Text från: ${extractTextStep.selector}`
        };
      }
      break;

    case 'extractAttribute':
      const extractAttrStep = step as any;
      if (extractAttrStep.variableName) {
        result.static[extractAttrStep.variableName] = {
          stepType: step.type,
          description: `Attribut ${extractAttrStep.attribute} från: ${extractAttrStep.selector}`
        };
      }
      break;

    case 'takeScreenshot':
      const screenshotStep = step as any;
      if (screenshotStep.variableName) {
        result.static[screenshotStep.variableName] = {
          stepType: step.type,
          description: `Base64 skärmdump${screenshotStep.path ? ` från: ${screenshotStep.path}` : ''}`
        };
      }
      break;

    case 'downloadFile':
      const downloadStep = step as any;
      if (downloadStep.variableName) {
        result.static[downloadStep.variableName] = {
          stepType: step.type,
          description: `Base64 filinnehåll${downloadStep.filename ? ` från: ${downloadStep.filename}` : ''}`
        };
      }
      break;

    case 'extractPdfValues':
      result.dynamic = {
        stepType: step.type,
        description: 'PDF-värden extraherade med AI',
        commonPatterns: ['var-namn', 'var-telefon', 'var-email', 'var-datum', 'var-belopp', 'var-adress', 'var-organisationsnummer', 'var-referens']
      };
      break;

    // Add more step types as needed
  }

  return result;
}

/**
 * Builds a complete variable registry from a list of steps
 */
export function buildVariableRegistry(steps: RpaStep[]): FlowVariableRegistry {
  const registry: FlowVariableRegistry = {
    static: {},
    dynamic: {}
  };

  steps.forEach(step => {
    const stepVars = getStepVariables(step);

    // Add static variables
    Object.entries(stepVars.static).forEach(([varName, varInfo]) => {
      registry.static[varName] = {
        stepId: step.id,
        stepType: varInfo.stepType,
        description: varInfo.description
      };
    });

    // Add dynamic variables
    if (stepVars.dynamic) {
      registry.dynamic[step.id] = stepVars.dynamic;
    }
  });

  return registry;
}

/**
 * Gets all available variables for a given step index (for use in editors)
 */
export function getAvailableVariables(steps: RpaStep[], currentStepIndex: number): {
  static: string[];
  dynamic: string[];
} {
  const availableSteps = steps.slice(0, currentStepIndex);
  const registry = buildVariableRegistry(availableSteps);

  const staticVars = Object.keys(registry.static);
  const dynamicVars: string[] = [];

  // Collect all common patterns from dynamic steps
  Object.values(registry.dynamic).forEach(dynamicInfo => {
    if (dynamicInfo.commonPatterns) {
      dynamicVars.push(...dynamicInfo.commonPatterns);
    }
  });

  return {
    static: staticVars,
    dynamic: [...new Set(dynamicVars)] // Remove duplicates
  };
}

/**
 * Variable interpolation utilities for RPA flows
 */

/**
 * Interpolates variables in a string using ${variableName} syntax
 * @param text The text containing variable references
 * @param variables The variables object containing values
 * @returns The text with variables replaced by their values
 */
export function interpolateVariables(text: string, variables: Record<string, any>): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Replace ${variableName} with actual values
  return text.replace(/\$\{([^}]+)\}/g, (match, variableName) => {
    const trimmedName = variableName.trim();

    if (trimmedName in variables) {
      const value = variables[trimmedName];
      // Convert to string, handling null/undefined
      return value != null ? String(value) : '';
    }

    // If variable not found, leave the placeholder as is
    return match;
  });
}

/**
 * Checks if a string contains variable references
 * @param text The text to check
 * @returns True if the text contains ${variableName} patterns
 */
export function hasVariableReferences(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }
  return /\$\{[^}]+\}/.test(text);
}

/**
 * Extracts all variable names referenced in a string
 * @param text The text to analyze
 * @returns Array of variable names found in the text
 */
export function extractVariableNames(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const matches = text.match(/\$\{([^}]+)\}/g);
  if (!matches) {
    return [];
  }

  return matches.map(match => {
    const variableName = match.slice(2, -1).trim(); // Remove ${ and }
    return variableName;
  });
}

/**
 * Validates that all variable references in a text can be resolved
 * @param text The text containing variable references
 * @param variables The available variables
 * @returns Object with validation result and missing variables
 */
export function validateVariableReferences(
  text: string,
  variables: Record<string, any>
): { valid: boolean; missingVariables: string[] } {
  const referencedVariables = extractVariableNames(text);
  const missingVariables = referencedVariables.filter(varName => !(varName in variables));

  return {
    valid: missingVariables.length === 0,
    missingVariables
  };
}

// Step label utilities
export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'navigate':
      return `Navigate to: ${step.url}`
    case 'click':
      return `Click: ${step.selector}`
    case 'fill':
      return `Fill: ${step.selector} = "${step.value}"`
    case 'type':
      return `Type: ${step.selector} = "${step.text}"`
    case 'waitForSelector':
      return `Wait for: ${step.selector}`
    case 'waitForTimeout':
      return `Wait: ${step.duration}ms`
    case 'extractText':
      return `Extract text: ${step.selector} → ${step.variableName}`
    case 'extractAttribute':
      return `Extract ${step.attribute}: ${step.selector} → ${step.variableName}`
    case 'fillPassword':
      return `Fill password: ${step.selector}`
    case 'fill2FA':
      return `Fill 2FA: ${step.selector}`
    case 'takeScreenshot':
      return `Take screenshot: ${step.path || 'screenshot.png'}`
    case 'ifElementExists':
      return `If element exists: ${step.selector}`
    case 'conditionalClick':
      const conditionalStep = step as ConditionalClickStep
      return `Conditional click: ${conditionalStep.selector} (if ${conditionalStep.condition})`
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep
      return `Download file: ${downloadStep.triggerSelector || 'trigger'} → ${downloadStep.filename || 'file'}`
    case 'extractPdfValues':
      const extractStep = step as ExtractPdfValuesStep
      return `Extract PDF values: ${extractStep.prompt.substring(0, 50)}...`
    case 'fortnoxCreateVoucher':
      const fortnoxStep = step as FortnoxCreateVoucherStep
      return `Create Fortnox voucher: ${fortnoxStep.description || 'New voucher'}`
    default:
      return step.type
  }
}
